#!/usr/bin/env python3
"""
Test script for Clado API integration with Atlas agent.

This script demonstrates how to use the Clado API through the DataProvidersTool.
Make sure to set your CLADO_API_KEY environment variable before running.

Usage:
    python test_clado_integration.py
"""

import os
import asyncio
from dotenv import load_dotenv
from agent.tools.data_providers_tool import DataProvidersTool

# Load environment variables
load_dotenv()

async def test_clado_integration():
    """Test the Clado API integration."""
    
    print("🔍 Testing Clado API Integration with Atlas Agent")
    print("=" * 50)
    
    # Check if API key is configured
    api_key = os.getenv("CLADO_API_KEY")
    if not api_key:
        print("❌ CLADO_API_KEY not found in environment variables")
        print("Please add your Clado API key to your .env file:")
        print("CLADO_API_KEY=your_api_key_here")
        return
    
    print(f"✅ API Key configured: {api_key[:10]}...")
    
    # Initialize the DataProvidersTool
    try:
        tool = DataProvidersTool()
        print("✅ DataProvidersTool initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize DataProvidersTool: {e}")
        return
    
    # Test 1: Get available endpoints
    print("\n📋 Test 1: Getting Clado endpoints...")
    try:
        result = await tool.get_data_provider_endpoints("clado")
        if result.success:
            endpoints = eval(result.output)  # Convert string back to dict
            print(f"✅ Found {len(endpoints)} Clado endpoints:")
            for name, endpoint in endpoints.items():
                print(f"   • {name}: {endpoint['name']}")
        else:
            print(f"❌ Failed to get endpoints: {result.output}")
            return
    except Exception as e:
        print(f"❌ Error getting endpoints: {e}")
        return
    
    # Test 2: Search for users (this will make an actual API call)
    print("\n👥 Test 2: Searching for users...")
    try:
        search_payload = {
            "query": "software engineers at Google",
            "limit": 3  # Small limit for testing
        }
        
        result = await tool.execute_data_provider_call(
            service_name="clado",
            route="search_users",
            payload=search_payload
        )
        
        if result.success:
            print("✅ User search successful!")
            # Parse the response to show summary
            import json
            response = json.loads(result.output)
            if "results" in response:
                print(f"   Found {len(response['results'])} profiles")
                for i, profile in enumerate(response['results'][:2]):  # Show first 2
                    profile_data = profile.get('profile', {})
                    print(f"   {i+1}. {profile_data.get('name', 'N/A')} - {profile_data.get('headline', 'N/A')}")
            else:
                print(f"   Response: {result.output[:200]}...")
        else:
            print(f"❌ User search failed: {result.output}")
    except Exception as e:
        print(f"❌ Error in user search: {e}")
    
    # Test 3: Search for companies
    print("\n🏢 Test 3: Searching for companies...")
    try:
        company_payload = {
            "query": "AI startups in healthcare",
            "limit": 2  # Small limit for testing
        }
        
        result = await tool.execute_data_provider_call(
            service_name="clado",
            route="search_companies",
            payload=company_payload
        )
        
        if result.success:
            print("✅ Company search successful!")
            import json
            response = json.loads(result.output)
            if "results" in response:
                print(f"   Found {len(response['results'])} companies")
                for i, company in enumerate(response['results'][:2]):  # Show first 2
                    print(f"   {i+1}. {company.get('company_name', 'N/A')} - {company.get('headquarters', 'N/A')}")
            else:
                print(f"   Response: {result.output[:200]}...")
        else:
            print(f"❌ Company search failed: {result.output}")
    except Exception as e:
        print(f"❌ Error in company search: {e}")
    
    # Test 4: Test WebSocket endpoint (should return not implemented message)
    print("\n🔌 Test 4: Testing WebSocket endpoint...")
    try:
        result = await tool.execute_data_provider_call(
            service_name="clado",
            route="websocket_search",
            payload={"query": "test"}
        )
        
        if result.success:
            import json
            response = json.loads(result.output)
            if "error" in response:
                print("✅ WebSocket endpoint correctly returns 'not implemented' message")
                print(f"   Message: {response['message']}")
            else:
                print("⚠️  Unexpected WebSocket response")
        else:
            print(f"❌ WebSocket test failed: {result.output}")
    except Exception as e:
        print(f"❌ Error testing WebSocket: {e}")
    
    print("\n🎉 Clado API Integration Test Complete!")
    print("\nThe Clado API is now fully integrated and ready for use by Atlas agent.")
    print("Atlas can now:")
    print("• Search for LinkedIn profiles using natural language")
    print("• Find companies with specific criteria")
    print("• Enrich profiles with detailed information")
    print("• Retrieve contact information")
    print("• Perform deep research with multiple query variations")

if __name__ == "__main__":
    asyncio.run(test_clado_integration())
