import os
import requests
from typing import Dict, Any, Optional
from agent.tools.data_providers.RapidDataProviderBase import EndpointSchema


class CladoProvider:
    """
    Clado API Provider for LinkedIn data search and enrichment.

    Provides access to all Clado API endpoints including:
    - Natural language search for users and companies
    - Profile enrichment and contact information retrieval
    - Deep research with async job processing
    - WebSocket real-time search capabilities
    """

    def __init__(self):
        self.base_url = "https://search.linkd.inc"
        self.api_key = os.getenv("CLADO_API_KEY")

        if not self.api_key:
            raise ValueError("CLADO_API_KEY environment variable is required")

        self.endpoints: Dict[str, EndpointSchema] = {
            "search_users": {
                "route": "/api/search/users",
                "method": "GET",
                "name": "Search for Users",
                "description": "Search through millions of LinkedIn profiles using natural language queries with AI-powered criteria matching",
                "payload": {
                    "query": "Natural language search query (e.g., 'software engineers in San Francisco')",
                    "limit": "Maximum number of results (default: 30, max: 100)",
                    "school": "Filter by school names (array)",
                    "company": "Filter by company names (array)",
                    "acceptance_threshold": "Match score threshold 0-100 (default: 73)",
                },
            },
            "search_companies": {
                "route": "/api/search/companies",
                "method": "GET",
                "name": "Search for Companies",
                "description": "Search for companies using natural language queries with AI-powered criteria matching",
                "payload": {
                    "query": "Natural language search query (e.g., 'AI startups in healthcare')",
                    "limit": "Maximum number of results (default: 30, max: 100)",
                    "acceptance_threshold": "Match score threshold 0-100 (default: 73)",
                },
            },
            "enrich_profile": {
                "route": "/api/enrich/linkedin",
                "method": "GET",
                "name": "Profile Enrichment",
                "description": "Retrieve detailed profile information using LinkedIn URL, email address, or phone number",
                "payload": {
                    "url": "LinkedIn profile URL to look up (optional)",
                    "email": "Email address to search for (optional)",
                    "phone": "Phone number to search for (optional)",
                },
            },
            "get_contacts": {
                "route": "/api/enrich/contacts",
                "method": "GET",
                "name": "Retrieve Contact Information",
                "description": "Get email addresses and phone numbers for LinkedIn profiles",
                "payload": {
                    "linkedin_url": "LinkedIn profile URL to look up (optional)",
                    "email": "Email address to search for (optional)",
                    "phone": "Phone number to search for (optional)",
                },
            },
            "complete_profile": {
                "route": "/api/profile/complete",
                "method": "GET",
                "name": "Complete LinkedIn Profile Retrieval",
                "description": "Get comprehensive LinkedIn profile data including all sections",
                "payload": {
                    "url": "LinkedIn profile URL",
                    "include_posts": "Include recent posts (true/false)",
                    "include_connections": "Include connection count (true/false)",
                },
            },
            "post_reactions": {
                "route": "/api/posts/reactions",
                "method": "GET",
                "name": "LinkedIn Post Reactions",
                "description": "Get reactions and engagement data for LinkedIn posts",
                "payload": {
                    "post_url": "LinkedIn post URL",
                    "include_comments": "Include comments (true/false)",
                    "limit": "Maximum number of reactions to return",
                },
            },
            "deep_research": {
                "route": "/api/search/deep_research",
                "method": "POST",
                "name": "Deep Research",
                "description": "Initiate advanced search with multiple variations and optional email enrichment",
                "payload": {
                    "query": "The search query to research",
                    "limit": "Maximum number of results (default: 30, max: 100)",
                    "school": "Filter by school names (array)",
                    "company": "Filter by company names (array)",
                    "enrich_emails": "Whether to enrich results with contact information (default: true)",
                    "acceptance_threshold": "Acceptance score threshold 0-100 (default: 85)",
                },
            },
            "deep_research_status": {
                "route": "/api/search/deep_research/{job_id}",
                "method": "GET",
                "name": "Deep Research Status",
                "description": "Get status and results of a deep research job",
                "payload": {"job_id": "Job ID returned from deep research initiation"},
            },
            "websocket_search": {
                "route": "/api/search/ws",
                "method": "WSS",
                "name": "WebSocket Search",
                "description": "Real-time search using WebSocket connection for live results",
                "payload": {
                    "query": "Search query for real-time results",
                    "type": "Search type (users, companies, etc.)",
                    "filters": "Additional search filters",
                },
            },
            "search_yc_companies": {
                "route": "/api/search/yc-companies",
                "method": "WSS",
                "name": "Search YC Companies",
                "description": "Search Y Combinator companies using WebSocket for real-time results",
                "payload": {
                    "query": "Search query for YC companies",
                    "batch": "Batch name or identifier",
                    "filters": "Additional filters for YC company search",
                },
            },
        }

    def get_endpoints(self) -> Dict[str, EndpointSchema]:
        """Get all available endpoints for this provider."""
        return self.endpoints

    def call_endpoint(
        self, route: str, payload: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Call a Clado API endpoint with the given parameters.

        Args:
            route: The endpoint route name
            payload: Parameters for the API call

        Returns:
            dict: The JSON response from the API

        Raises:
            ValueError: If endpoint not found or API key missing
            requests.RequestException: If API call fails
        """
        if route.startswith("/"):
            route = route[1:]

        endpoint = self.endpoints.get(route)
        if not endpoint:
            raise ValueError(
                f"Endpoint {route} not found. Available endpoints: {list(self.endpoints.keys())}"
            )

        # Handle job_id parameter for deep research status
        url = f"{self.base_url}{endpoint['route']}"
        if "{job_id}" in url and payload and "job_id" in payload:
            url = url.replace("{job_id}", payload["job_id"])
            # Remove job_id from payload since it's now in the URL
            payload = {k: v for k, v in payload.items() if k != "job_id"}

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        method = endpoint.get("method", "GET").upper()

        # Handle WebSocket endpoints
        if method == "WSS":
            return {
                "error": "WebSocket endpoints not yet implemented",
                "message": f"WebSocket endpoint '{route}' requires additional implementation for real-time connections",
                "endpoint_info": endpoint,
            }

        try:
            if method == "GET":
                response = requests.get(url, params=payload, headers=headers)
            elif method == "POST":
                response = requests.post(url, json=payload, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # Raise an exception for bad status codes
            response.raise_for_status()

            return response.json()

        except requests.exceptions.HTTPError as e:
            if response.status_code == 401:
                raise ValueError("Invalid or missing Clado API key")
            elif response.status_code == 402:
                raise ValueError("Insufficient credits in Clado account")
            elif response.status_code == 422:
                raise ValueError("Missing required parameters for Clado API call")
            else:
                raise ValueError(
                    f"Clado API error ({response.status_code}): {response.text}"
                )
        except requests.exceptions.RequestException as e:
            raise ValueError(f"Failed to connect to Clado API: {str(e)}")


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    # Test the provider
    provider = CladoProvider()

    # Test search users
    try:
        result = provider.call_endpoint(
            route="search_users",
            payload={"query": "software engineers at Google", "limit": 5},
        )
        print("Search Users Result:")
        print(result)
    except Exception as e:
        print(f"Error testing search_users: {e}")
