#!/usr/bin/env python3
"""
End-to-End Test for Clado API Integration with Atlas Agent.

This script tests the complete agentic pipeline:
1. Agent receives user prompt
2. Agent decides to use Clado tool
3. Tool executes and returns results
4. Agent interprets results and responds
5. Frontend receives properly formatted tool results

Usage:
    python test_clado_e2e.py

Requirements:
    - CLADO_API_KEY environment variable must be set
    - Supabase connection configured
    - All dependencies installed
"""

import asyncio
import os
import sys
import json
import uuid
from typing import Dict, Any, List

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent.run import run_agent
from utils.logger import logger
from utils.config import config
from supabase import create_client, Client


class CladoE2ETest:
    """End-to-end test for Clado integration with Atlas agent."""
    
    def __init__(self):
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_ANON_KEY)
        self.test_thread_id = None
        self.test_account_id = "test-account-clado-e2e"
        
    async def setup_test_thread(self) -> str:
        """Create a test thread for the E2E test."""
        thread_id = f"test-clado-e2e-{uuid.uuid4().hex[:8]}"
        
        # Create thread in database
        thread_data = {
            "thread_id": thread_id,
            "account_id": self.test_account_id,
            "title": "Clado E2E Test",
            "created_at": "now()",
            "updated_at": "now()"
        }
        
        result = await self.supabase.table("threads").insert(thread_data).execute()
        if not result.data:
            raise Exception("Failed to create test thread")
            
        self.test_thread_id = thread_id
        logger.info(f"Created test thread: {thread_id}")
        return thread_id
    
    async def add_user_message(self, thread_id: str, content: str) -> str:
        """Add a user message to the thread."""
        message_data = {
            "thread_id": thread_id,
            "type": "user",
            "content": json.dumps({"content": content}),
            "created_at": "now()"
        }
        
        result = await self.supabase.table("messages").insert(message_data).execute()
        if not result.data:
            raise Exception("Failed to add user message")
            
        message_id = result.data[0]["message_id"]
        logger.info(f"Added user message: {content[:50]}...")
        return message_id
    
    async def get_thread_messages(self, thread_id: str) -> List[Dict[str, Any]]:
        """Get all messages from the thread."""
        result = await self.supabase.table("messages").select("*").eq("thread_id", thread_id).order("created_at").execute()
        return result.data if result.data else []
    
    async def cleanup_test_thread(self, thread_id: str):
        """Clean up test thread and messages."""
        try:
            # Delete messages first
            await self.supabase.table("messages").delete().eq("thread_id", thread_id).execute()
            # Delete thread
            await self.supabase.table("threads").delete().eq("thread_id", thread_id).execute()
            logger.info(f"Cleaned up test thread: {thread_id}")
        except Exception as e:
            logger.warning(f"Failed to cleanup test thread: {e}")
    
    async def run_test_scenario(self, user_prompt: str, expected_tool: str) -> Dict[str, Any]:
        """Run a complete test scenario."""
        print(f"\n🧪 Testing Scenario: {user_prompt}")
        print("=" * 80)
        
        # Setup
        thread_id = await self.setup_test_thread()
        await self.add_user_message(thread_id, user_prompt)
        
        # Track results
        results = {
            "user_prompt": user_prompt,
            "expected_tool": expected_tool,
            "thread_id": thread_id,
            "agent_responses": [],
            "tool_calls": [],
            "tool_results": [],
            "success": False,
            "error": None
        }
        
        try:
            print(f"🤖 Running agent for thread: {thread_id}")
            
            # Run the agent
            response_count = 0
            async for response in run_agent(
                thread_id=thread_id,
                model_name="anthropic/claude-3-5-sonnet-20241022",
                stream=True,
                max_iterations=3
            ):
                response_count += 1
                print(f"📨 Response {response_count}: {response.get('type', 'unknown')}")
                
                if response.get("type") == "error":
                    results["error"] = response.get("message", "Unknown error")
                    print(f"❌ Error: {results['error']}")
                    break
                    
                # Track different response types
                if response.get("type") == "assistant":
                    results["agent_responses"].append(response)
                    print(f"💬 Assistant: {response.get('content', '')[:100]}...")
                    
                elif response.get("type") == "tool_started":
                    tool_name = response.get("function_name") or response.get("xml_tag_name")
                    print(f"🔧 Tool Started: {tool_name}")
                    
                elif response.get("type") == "tool":
                    # This is the actual tool result
                    results["tool_results"].append(response)
                    tool_content = response.get("content", {})
                    
                    # Parse tool execution details
                    if isinstance(tool_content, dict) and "tool_execution" in tool_content:
                        tool_exec = tool_content["tool_execution"]
                        tool_name = tool_exec.get("function_name", "unknown")
                        tool_success = tool_exec.get("result", {}).get("success", False)
                        tool_output = tool_exec.get("result", {}).get("output", "")
                        
                        results["tool_calls"].append({
                            "tool_name": tool_name,
                            "success": tool_success,
                            "output_length": len(str(tool_output))
                        })
                        
                        print(f"🛠️  Tool Result: {tool_name} - {'✅ Success' if tool_success else '❌ Failed'}")
                        if tool_success and tool_output:
                            # Try to parse and show summary
                            try:
                                if isinstance(tool_output, str):
                                    output_data = json.loads(tool_output)
                                else:
                                    output_data = tool_output
                                    
                                if isinstance(output_data, dict):
                                    summary_keys = ["results_returned", "total_results", "job_id", "status"]
                                    summary = {k: output_data.get(k) for k in summary_keys if k in output_data}
                                    print(f"📊 Summary: {summary}")
                            except:
                                print(f"📄 Output: {str(tool_output)[:200]}...")
                    
                elif response.get("type") == "tool_completed":
                    tool_name = response.get("function_name") or response.get("xml_tag_name")
                    print(f"✅ Tool Completed: {tool_name}")
                    
                elif response.get("type") == "tool_failed":
                    tool_name = response.get("function_name") or response.get("xml_tag_name")
                    print(f"❌ Tool Failed: {tool_name}")
            
            # Analyze results
            clado_tool_used = any(
                tool["tool_name"].startswith("search_linkedin") or 
                tool["tool_name"].startswith("enrich_linkedin") or
                tool["tool_name"].startswith("get_linkedin") or
                tool["tool_name"].startswith("start_deep") or
                tool["tool_name"].startswith("scrape_linkedin")
                for tool in results["tool_calls"]
            )
            
            successful_tool_calls = [tool for tool in results["tool_calls"] if tool["success"]]
            
            results["success"] = (
                clado_tool_used and 
                len(successful_tool_calls) > 0 and
                len(results["agent_responses"]) > 0 and
                not results["error"]
            )
            
            # Print summary
            print(f"\n📋 Test Summary:")
            print(f"   Clado Tool Used: {'✅' if clado_tool_used else '❌'}")
            print(f"   Successful Tool Calls: {len(successful_tool_calls)}")
            print(f"   Agent Responses: {len(results['agent_responses'])}")
            print(f"   Overall Success: {'✅' if results['success'] else '❌'}")
            
            if results["error"]:
                print(f"   Error: {results['error']}")
                
        except Exception as e:
            results["error"] = str(e)
            results["success"] = False
            print(f"❌ Test failed with exception: {e}")
            logger.error(f"E2E test failed: {e}")
            
        finally:
            # Cleanup
            await self.cleanup_test_thread(thread_id)
            
        return results


async def main():
    """Run the complete E2E test suite."""
    print("🚀 Clado API End-to-End Test Suite")
    print("=" * 80)
    
    # Check prerequisites
    if not config.CLADO_API_KEY:
        print("❌ CLADO_API_KEY not found. Please set your API key.")
        return False
    
    print(f"✅ CLADO_API_KEY configured")
    print(f"✅ Supabase URL: {config.SUPABASE_URL}")
    
    # Initialize test runner
    test_runner = CladoE2ETest()
    
    # Define test scenarios
    test_scenarios = [
        {
            "prompt": "Find me 3 software engineers who work at Google",
            "expected_tool": "search_linkedin_users"
        },
        {
            "prompt": "Research AI startups in San Francisco and show me 2 companies",
            "expected_tool": "search_linkedin_companies"
        },
        {
            "prompt": "Get detailed profile information for this LinkedIn URL: https://www.linkedin.com/in/satyanadella",
            "expected_tool": "enrich_linkedin_profile"
        }
    ]
    
    # Run test scenarios
    all_results = []
    successful_tests = 0
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🧪 Test {i}/{len(test_scenarios)}")
        result = await test_runner.run_test_scenario(
            scenario["prompt"], 
            scenario["expected_tool"]
        )
        all_results.append(result)
        
        if result["success"]:
            successful_tests += 1
            print(f"✅ Test {i} PASSED")
        else:
            print(f"❌ Test {i} FAILED: {result.get('error', 'Unknown error')}")
    
    # Final summary
    print(f"\n🎯 Final Results")
    print("=" * 80)
    print(f"Tests Passed: {successful_tests}/{len(test_scenarios)}")
    print(f"Success Rate: {(successful_tests/len(test_scenarios)*100):.1f}%")
    
    if successful_tests == len(test_scenarios):
        print("🎉 All tests passed! Clado integration is working end-to-end.")
        return True
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
