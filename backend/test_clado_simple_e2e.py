#!/usr/bin/env python3
"""
Simple End-to-End Test for Clado Tool Integration.

This test verifies that the Clado tool can be properly invoked and returns
structured results that would be compatible with the frontend.

Usage:
    python test_clado_simple_e2e.py
"""

import asyncio
import os
import sys
import json
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent.tools.clado_tool import CladoTool
from utils.logger import logger


def simulate_structured_tool_result(tool_call: Dict[str, Any], result) -> Dict[str, Any]:
    """
    Simulate how the response processor creates structured tool results.
    This mimics the _create_structured_tool_result method.
    """
    function_name = tool_call.get("function_name", "unknown")
    xml_tag_name = tool_call.get("xml_tag_name")
    arguments = tool_call.get("arguments", {})
    tool_call_id = tool_call.get("id")
    
    # Parse the output (which is JSON string from ToolResult)
    try:
        if hasattr(result, 'output') and isinstance(result.output, str):
            output = json.loads(result.output)
        else:
            output = str(result.output) if hasattr(result, 'output') else str(result)
    except json.JSONDecodeError:
        output = str(result.output) if hasattr(result, 'output') else str(result)
    
    # Create the structured result that the frontend expects
    structured_result = {
        "tool_execution": {
            "function_name": function_name,
            "xml_tag_name": xml_tag_name,
            "tool_call_id": tool_call_id,
            "arguments": arguments,
            "result": {
                "success": result.success if hasattr(result, 'success') else True,
                "output": output,
                "error": getattr(result, 'error', None) if hasattr(result, 'error') else None
            }
        }
    }
    
    return structured_result


async def test_clado_tool_pipeline():
    """Test the complete Clado tool pipeline with structured results."""
    
    print("🧪 Testing Clado Tool Pipeline")
    print("=" * 60)
    
    # Check API key
    if not os.getenv("CLADO_API_KEY"):
        print("❌ CLADO_API_KEY not found. Please set your API key.")
        return False
    
    try:
        # Initialize Clado tool
        print("🔧 Initializing CladoTool...")
        clado_tool = CladoTool()
        print("✅ CladoTool initialized successfully")
        
        # Test scenarios that simulate how Atlas would call the tool
        test_scenarios = [
            {
                "name": "LinkedIn User Search",
                "tool_call": {
                    "function_name": "search_linkedin_users",
                    "xml_tag_name": "search-linkedin-users",
                    "id": "call_001",
                    "arguments": {
                        "query": "software engineers at Google",
                        "limit": 3,
                        "acceptance_threshold": 80
                    }
                },
                "method": clado_tool.search_linkedin_users,
                "args": {
                    "query": "software engineers at Google",
                    "limit": 3,
                    "acceptance_threshold": 80
                }
            },
            {
                "name": "LinkedIn Company Search", 
                "tool_call": {
                    "function_name": "search_linkedin_companies",
                    "xml_tag_name": "search-linkedin-companies",
                    "id": "call_002",
                    "arguments": {
                        "query": "AI startups in San Francisco",
                        "limit": 2,
                        "acceptance_threshold": 75
                    }
                },
                "method": clado_tool.search_linkedin_companies,
                "args": {
                    "query": "AI startups in San Francisco", 
                    "limit": 2,
                    "acceptance_threshold": 75
                }
            },
            {
                "name": "Profile Enrichment",
                "tool_call": {
                    "function_name": "enrich_linkedin_profile",
                    "xml_tag_name": "enrich-linkedin-profile", 
                    "id": "call_003",
                    "arguments": {
                        "url": "https://www.linkedin.com/in/satyanadella"
                    }
                },
                "method": clado_tool.enrich_linkedin_profile,
                "args": {
                    "url": "https://www.linkedin.com/in/satyanadella"
                }
            }
        ]
        
        results = []
        successful_tests = 0
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🧪 Test {i}: {scenario['name']}")
            print("-" * 40)
            
            try:
                # Execute the tool method
                print(f"🔧 Calling {scenario['tool_call']['function_name']}...")
                result = await scenario["method"](**scenario["args"])
                
                # Check if tool execution was successful
                if result.success:
                    print(f"✅ Tool execution successful")
                    
                    # Create structured result (simulating response processor)
                    structured_result = simulate_structured_tool_result(scenario["tool_call"], result)
                    
                    # Validate structured result format
                    tool_execution = structured_result.get("tool_execution", {})
                    tool_result = tool_execution.get("result", {})
                    
                    print(f"📊 Function: {tool_execution.get('function_name')}")
                    print(f"📊 Success: {tool_result.get('success')}")
                    print(f"📊 Output Type: {type(tool_result.get('output'))}")
                    
                    # Try to extract meaningful data from output
                    output = tool_result.get('output')
                    if isinstance(output, dict):
                        summary_keys = ["results_returned", "total_results", "job_id", "status", "cost"]
                        summary = {k: output.get(k) for k in summary_keys if k in output}
                        if summary:
                            print(f"📊 Summary: {summary}")
                    
                    # Validate that frontend can parse this
                    frontend_compatible = (
                        "tool_execution" in structured_result and
                        "function_name" in tool_execution and
                        "result" in tool_execution and
                        "success" in tool_result and
                        "output" in tool_result
                    )
                    
                    print(f"🖥️  Frontend Compatible: {'✅' if frontend_compatible else '❌'}")
                    
                    if frontend_compatible:
                        successful_tests += 1
                        
                    results.append({
                        "scenario": scenario["name"],
                        "success": True,
                        "frontend_compatible": frontend_compatible,
                        "structured_result": structured_result
                    })
                    
                else:
                    print(f"❌ Tool execution failed: {result.output}")
                    results.append({
                        "scenario": scenario["name"],
                        "success": False,
                        "error": result.output
                    })
                    
            except Exception as e:
                print(f"❌ Test failed with exception: {str(e)}")
                results.append({
                    "scenario": scenario["name"],
                    "success": False,
                    "error": str(e)
                })
        
        # Final summary
        print(f"\n🎯 Pipeline Test Results")
        print("=" * 60)
        print(f"Tests Passed: {successful_tests}/{len(test_scenarios)}")
        print(f"Success Rate: {(successful_tests/len(test_scenarios)*100):.1f}%")
        
        # Check if all tests passed
        if successful_tests == len(test_scenarios):
            print("🎉 All tests passed! Clado tool pipeline is working correctly.")
            print("✅ Tool execution works")
            print("✅ Structured results are properly formatted")
            print("✅ Frontend compatibility verified")
            return True
        else:
            print("⚠️  Some tests failed. Check the details above.")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {str(e)}")
        logger.error(f"Clado pipeline test failed: {str(e)}")
        return False


async def test_frontend_tool_result_format():
    """Test that tool results match the expected frontend format."""
    
    print("\n🖥️  Testing Frontend Tool Result Format")
    print("=" * 60)
    
    # Sample structured result that would be sent to frontend
    sample_structured_result = {
        "tool_execution": {
            "function_name": "search_linkedin_users",
            "xml_tag_name": "search-linkedin-users",
            "tool_call_id": "call_123",
            "arguments": {
                "query": "software engineers at Google",
                "limit": 5
            },
            "result": {
                "success": True,
                "output": {
                    "query": "software engineers at Google",
                    "total_results": 5,
                    "results_returned": 5,
                    "results": [
                        {"name": "John Doe", "title": "Software Engineer", "company": "Google"},
                        {"name": "Jane Smith", "title": "Senior Software Engineer", "company": "Google"}
                    ],
                    "cost": "5 credits used"
                },
                "error": None
            }
        }
    }
    
    # Validate format matches frontend expectations
    checks = [
        ("Has tool_execution", "tool_execution" in sample_structured_result),
        ("Has function_name", "function_name" in sample_structured_result["tool_execution"]),
        ("Has xml_tag_name", "xml_tag_name" in sample_structured_result["tool_execution"]),
        ("Has arguments", "arguments" in sample_structured_result["tool_execution"]),
        ("Has result", "result" in sample_structured_result["tool_execution"]),
        ("Has success flag", "success" in sample_structured_result["tool_execution"]["result"]),
        ("Has output", "output" in sample_structured_result["tool_execution"]["result"]),
        ("Output is structured", isinstance(sample_structured_result["tool_execution"]["result"]["output"], dict))
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 Frontend Format Validation: {'✅ PASSED' if all_passed else '❌ FAILED'}")
    return all_passed


async def main():
    """Run all pipeline tests."""
    print("🚀 Clado Tool Pipeline Test Suite")
    print("=" * 80)
    
    # Run tool pipeline test
    pipeline_success = await test_clado_tool_pipeline()
    
    # Run frontend format test
    frontend_success = await test_frontend_tool_result_format()
    
    # Overall result
    overall_success = pipeline_success and frontend_success
    
    print(f"\n🏁 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print("🎉 Clado integration is ready for production!")
        print("   ✅ Tool execution pipeline works")
        print("   ✅ Structured results are properly formatted")
        print("   ✅ Frontend compatibility confirmed")
    else:
        print("⚠️  Integration needs attention before production deployment")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
